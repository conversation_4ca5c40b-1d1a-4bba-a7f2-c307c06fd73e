// 购买会员卡页
const app = getApp()

Page({
  data: {
    customerId: '',
    customer: {},
    membershipInfo: null,
    cards: [],
    selectedCardId: '',
    paymentAmount: '',
    operations: [
      { id: 'purchase', name: '购买新卡' },
      { id: 'renew', name: '续费' }
    ],
    operationIndex: 0
  },

  onLoad: function (options) {
    if (options.customerId) {
      this.setData({
        customerId: options.customerId
      })
      this.loadCustomerInfo()
      this.loadCards()
    } else {
      wx.showToast({
        title: '客户ID不存在',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载客户信息
  loadCustomerInfo: function () {
    const { customerId } = this.data
    
    wx.showLoading({
      title: '加载中...',
    })
    
    // 获取客户基本信息
    wx.cloud.callFunction({
      name: 'customerService',
      data: {
        action: 'getCustomerDetail',
        data: {
          customerId
        }
      }
    }).then(res => {
      const { success, data } = res.result
      
      if (success && data) {
        this.setData({
          customer: data
        })
        
        // 获取客户会员信息
        return wx.cloud.callFunction({
          name: 'customerService',
          data: {
            action: 'getMembershipInfo',
            data: {
              customerId
            }
          }
        })
      } else {
        wx.showToast({
          title: '获取客户信息失败',
          icon: 'none'
        })
        return Promise.reject('获取客户信息失败')
      }
    }).then(res => {
      const { success, data } = res.result
      
      if (success) {
        this.setData({
          membershipInfo: data
        })
      }
    }).catch(err => {
      console.error('获取客户信息失败', err)
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 加载会员卡列表
  loadCards: function () {
    wx.showLoading({
      title: '加载中...',
    })
    
    wx.cloud.callFunction({
      name: 'membershipService',
      data: {
        action: 'getCardList',
        data: {
          page: 1,
          pageSize: 100
        }
      }
    }).then(res => {
      const { success, data } = res.result
      
      if (success) {
        this.setData({
          cards: data
        })
        
        // 如果有会员卡，默认选中第一个
        if (data.length > 0) {
          this.setData({
            selectedCardId: data[0]._id,
            paymentAmount: data[0].price
          })
        }
      } else {
        wx.showToast({
          title: '获取会员卡列表失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取会员卡列表失败', err)
      wx.showToast({
        title: '获取会员卡列表失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 选择会员卡
  selectCard: function (e) {
    const cardId = e.currentTarget.dataset.id
    const card = this.data.cards.find(item => item._id === cardId)
    
    this.setData({
      selectedCardId: cardId,
      paymentAmount: card ? card.price : ''
    })
  },

  // 支付金额输入
  onPaymentInput: function (e) {
    this.setData({
      paymentAmount: e.detail.value
    })
  },

  // 操作类型选择
  bindOperationChange: function (e) {
    this.setData({
      operationIndex: e.detail.value
    })
  },

  // 提交表单
  submitForm: function () {
    const { customerId, selectedCardId, paymentAmount, operationIndex, operations } = this.data
    
    // 表单验证
    if (!selectedCardId) {
      wx.showToast({
        title: '请选择会员卡',
        icon: 'none'
      })
      return
    }
    
    if (!paymentAmount) {
      wx.showToast({
        title: '请输入支付金额',
        icon: 'none'
      })
      return
    }
    
    wx.showLoading({
      title: '处理中...',
    })
    
    const operation = operations[operationIndex].id
    const action = operation === 'purchase' ? 'purchaseCard' : 'renewCard'
    
    wx.cloud.callFunction({
      name: 'membershipService',
      data: {
        action,
        data: {
          customerId,
          cardId: selectedCardId,
          paymentAmount: parseFloat(paymentAmount)
        }
      }
    }).then(res => {
      const { success, message } = res.result
      
      if (success) {
        wx.showToast({
          title: operation === 'purchase' ? '购买成功' : '续费成功',
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: message || (operation === 'purchase' ? '购买失败' : '续费失败'),
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error(operation === 'purchase' ? '购买会员卡失败' : '续费会员卡失败', err)
      wx.showToast({
        title: operation === 'purchase' ? '购买失败' : '续费失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 取消表单
  cancelForm: function () {
    wx.navigateBack()
  }
})
