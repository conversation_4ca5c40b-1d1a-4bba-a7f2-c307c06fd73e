// 添加记录页
const app = getApp()

Page({
  data: {
    customerId: '', // 从客户详情页传入的客户ID
    recordType: '', // 从客户详情页传入的记录类型
    customers: [],
    customerIndex: -1,
    recordTypes: [
      { id: 'training', name: '训练记录' },
      { id: 'purchase', name: '购买记录' }
    ],
    typeIndex: -1,
    currentType: '', // 当前选择的记录类型
    trainingTypes: [
      { id: 'eye', name: '眼睛训练' },
      { id: 'vision', name: '视觉训练' }
    ],
    trainingTypeIndex: -1,
    products: [],
    productIndex: -1
  },

  onLoad: function (options) {
    // 如果从客户详情页传入了客户ID
    if (options.customerId) {
      this.setData({
        customerId: options.customerId
      })
    }

    // 如果从客户详情页传入了记录类型
    if (options.type) {
      const type = options.type === 'training' ? 'training' : 'purchase'
      const typeIndex = this.data.recordTypes.findIndex(item => item.id === type)

      this.setData({
        recordType: type,
        typeIndex: typeIndex >= 0 ? typeIndex : 0,
        currentType: type
      })

      // 如果是购买记录，加载产品列表
      if (type === 'purchase') {
        this.loadProducts()
      }
    }

    // 加载客户列表
    this.loadCustomers()
  },

  // 加载客户列表
  loadCustomers: function () {
    wx.showLoading({
      title: '加载中...',
    })

    wx.cloud.callFunction({
      name: 'customerService',
      data: {
        action: 'getCustomerList',
        data: {
          page: 1,
          pageSize: 100
        }
      }
    }).then(res => {
      const { success, data } = res.result

      if (success) {
        this.setData({
          customers: data
        })

        // 如果有客户ID，设置选中的客户
        if (this.data.customerId) {
          const index = data.findIndex(item => item._id === this.data.customerId)
          if (index >= 0) {
            this.setData({
              customerIndex: index
            })
          }
        }
      } else {
        wx.showToast({
          title: '获取客户列表失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取客户列表失败', err)
      wx.showToast({
        title: '获取客户列表失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 加载产品列表
  loadProducts: function () {
    wx.showLoading({
      title: '加载中...',
    })

    wx.cloud.callFunction({
      name: 'productService',
      data: {
        action: 'getProductList',
        data: {
          page: 1,
          pageSize: 100
        }
      }
    }).then(res => {
      const { success, data } = res.result

      if (success) {
        this.setData({
          products: data
        })
      } else {
        wx.showToast({
          title: '获取产品列表失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取产品列表失败', err)
      wx.showToast({
        title: '获取产品列表失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 客户选择器变化
  bindCustomerChange: function (e) {
    this.setData({
      customerIndex: e.detail.value
    })
  },

  // 记录类型选择器变化
  bindTypeChange: function (e) {
    const index = e.detail.value
    const type = this.data.recordTypes[index].id

    this.setData({
      typeIndex: index,
      currentType: type
    })

    // 如果是购买记录，加载产品列表
    if (type === 'purchase') {
      this.loadProducts()
    }
  },

  // 训练类型选择器变化
  bindTrainingTypeChange: function (e) {
    this.setData({
      trainingTypeIndex: e.detail.value
    })
  },

  // 产品选择器变化
  bindProductChange: function (e) {
    this.setData({
      productIndex: e.detail.value
    })
  },

  // 提交训练记录表单
  submitTrainingForm: function (e) {
    const formData = e.detail.value
    const { customerIndex, trainingTypeIndex, customers, trainingTypes } = this.data

    // 表单验证
    if (customerIndex < 0) {
      wx.showToast({
        title: '请选择客户',
        icon: 'none'
      })
      return
    }

    if (trainingTypeIndex < 0) {
      wx.showToast({
        title: '请选择训练类型',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '添加中...',
    })

    // 构建数据
    const data = {
      customerId: customers[customerIndex]._id,
      type: trainingTypes[trainingTypeIndex].id,
      duration: formData.duration ? parseInt(formData.duration) : 0,
      content: formData.content || ''
    }

    wx.cloud.callFunction({
      name: 'recordService',
      data: {
        action: 'addTrainingRecord',
        data
      }
    }).then(res => {
      const { success, message } = res.result

      if (success) {
        wx.showToast({
          title: '添加成功',
        })
        setTimeout(() => {
          this.safeNavigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: message || '添加失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('添加训练记录失败', err)
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 提交购买记录表单
  submitPurchaseForm: function (e) {
    const formData = e.detail.value
    const { customerIndex, productIndex, customers, products } = this.data

    // 表单验证
    if (customerIndex < 0) {
      wx.showToast({
        title: '请选择客户',
        icon: 'none'
      })
      return
    }

    if (productIndex < 0) {
      wx.showToast({
        title: '请选择产品',
        icon: 'none'
      })
      return
    }

    if (!formData.amount) {
      wx.showToast({
        title: '请输入金额',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '添加中...',
    })

    // 构建数据
    const data = {
      customerId: customers[customerIndex]._id,
      productId: products[productIndex]._id,
      amount: parseFloat(formData.amount),
      remark: formData.remark || ''
    }

    wx.cloud.callFunction({
      name: 'recordService',
      data: {
        action: 'addPurchaseRecord',
        data
      }
    }).then(res => {
      const { success, message } = res.result

      if (success) {
        wx.showToast({
          title: '添加成功',
        })
        setTimeout(() => {
          this.safeNavigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: message || '添加失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('添加购买记录失败', err)
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      })
    }).finally(() => {
      wx.hideLoading()
    })
  },

  // 取消表单
  cancelForm: function () {
    this.safeNavigateBack()
  },

  // 安全的返回导航
  safeNavigateBack: function() {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      wx.navigateBack()
    } else {
      // 如果只有一个页面，跳转到客户列表页
      wx.redirectTo({
        url: '/pages/customer/list/index'
      })
    }
  }
})
