// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event

  switch (action) {
    case 'getCustomerList':
      return getCustomerList(data)
    case 'getCustomerDetail':
      return getCustomerDetail(data)
    case 'addCustomer':
      return addCustomer(data)
    case 'updateCustomer':
      return updateCustomer(data)
    case 'deleteCustomer':
      return deleteCustomer(data)
    case 'updateMembership':
      return updateMembership(data)
    case 'getMembershipInfo':
      return getMembershipInfo(data)
    default:
      return {
        success: false,
        message: '未知操作'
      }
  }
}

// 获取客户列表
async function getCustomerList(data) {
  const { keyword = '', page = 1, pageSize = 10 } = data

  try {
    // 构建查询条件
    let query = {}
    if (keyword) {
      query = {
        name: db.RegExp({
          regexp: keyword,
          options: 'i',
        })
      }
    }

    // 计算总数
    const countResult = await db.collection('customers').where(query).count()
    const total = countResult.total

    // 查询客户列表
    const customers = await db.collection('customers')
      .where(query)
      .orderBy('createTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()

    // 查询每个客户的最近训练记录
    const customerIds = customers.data.map(customer => customer._id)

    // 聚合查询每个客户的最近训练记录
    const trainingRecordsPromises = customerIds.map(async (customerId) => {
      const records = await db.collection('training_records')
        .where({
          customerId
        })
        .orderBy('createTime', 'desc')
        .limit(1)
        .get()

      return {
        customerId,
        record: records.data.length > 0 ? records.data[0] : null
      }
    })

    const trainingRecords = await Promise.all(trainingRecordsPromises)

    // 将训练记录合并到客户数据中
    const result = customers.data.map(customer => {
      const record = trainingRecords.find(item => item.customerId === customer._id)
      return {
        ...customer,
        lastTraining: record ? record.record : null
      }
    })

    return {
      success: true,
      data: result,
      total,
      page,
      pageSize
    }
  } catch (error) {
    return {
      success: false,
      message: '获取客户列表失败',
      error
    }
  }
}

// 获取客户详情
async function getCustomerDetail(data) {
  const { customerId } = data

  try {
    // 查询客户信息
    const customer = await db.collection('customers').doc(customerId).get()

    if (!customer.data) {
      return {
        success: false,
        message: '客户不存在'
      }
    }

    // 查询客户的训练记录
    const trainingRecords = await db.collection('training_records')
      .where({
        customerId
      })
      .orderBy('createTime', 'desc')
      .limit(10)
      .get()

    // 查询客户的购买记录
    const purchaseRecords = await db.collection('purchase_records')
      .where({
        customerId
      })
      .orderBy('createTime', 'desc')
      .limit(10)
      .get()

    return {
      success: true,
      data: {
        ...customer.data,
        trainingRecords: trainingRecords.data,
        purchaseRecords: purchaseRecords.data
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '获取客户详情失败',
      error
    }
  }
}

// 添加客户
async function addCustomer(data) {
  try {
    const result = await db.collection('customers').add({
      data: {
        ...data,
        createTime: db.serverDate()
      }
    })

    return {
      success: true,
      message: '添加客户成功',
      data: {
        _id: result._id
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '添加客户失败',
      error
    }
  }
}

// 更新客户
async function updateCustomer(data) {
  const { _id, ...updateData } = data

  try {
    await db.collection('customers').doc(_id).update({
      data: {
        ...updateData,
        updateTime: db.serverDate()
      }
    })

    return {
      success: true,
      message: '更新客户成功'
    }
  } catch (error) {
    return {
      success: false,
      message: '更新客户失败',
      error
    }
  }
}

// 删除客户
async function deleteCustomer(data) {
  const { customerId } = data

  try {
    await db.collection('customers').doc(customerId).remove()

    return {
      success: true,
      message: '删除客户成功'
    }
  } catch (error) {
    return {
      success: false,
      message: '删除客户失败',
      error
    }
  }
}

// 更新客户会员信息
async function updateMembership(data) {
  const { customerId, membership } = data

  try {
    const updateData = {
      updateTime: db.serverDate()
    }

    // 如果提供了完整的membership对象
    if (membership) {
      updateData.membership = membership
    } else {
      // 兼容旧版本的参数
      const { membershipCardId, memberExpireDate, remainingTrainingCount } = data

      // 先获取客户当前信息
      const customer = await db.collection('customers').doc(customerId).get()

      if (!customer.data) {
        return {
          success: false,
          message: '客户不存在'
        }
      }

      // 构建membership对象
      const membershipData = customer.data.membership || {}

      if (membershipCardId !== undefined) {
        membershipData.cardId = membershipCardId
      }

      if (memberExpireDate !== undefined) {
        membershipData.expireDate = memberExpireDate
      }

      if (remainingTrainingCount !== undefined) {
        membershipData.remainingTrainingCount = remainingTrainingCount
      }

      updateData.membership = membershipData
    }

    await db.collection('customers').doc(customerId).update({
      data: updateData
    })

    return {
      success: true,
      message: '更新客户会员信息成功'
    }
  } catch (error) {
    return {
      success: false,
      message: '更新客户会员信息失败',
      error
    }
  }
}

// 获取客户会员信息
async function getMembershipInfo(data) {
  const { customerId } = data

  try {
    // 查询客户信息
    const customer = await db.collection('customers').doc(customerId).get()

    if (!customer.data) {
      return {
        success: false,
        message: '客户不存在'
      }
    }

    // 检查客户是否有会员信息
    if (!customer.data.membership) {
      return {
        success: true,
        data: {
          isMember: false
        }
      }
    }

    // 如果客户有会员卡，查询会员卡信息
    let membershipCard = null
    if (customer.data.membership.cardId) {
      const card = await db.collection('products').doc(customer.data.membership.cardId).get()
      if (card.data && card.data.type === 'membership') {
        membershipCard = card.data
      }
    }

    // 判断会员是否有效
    const now = new Date()
    const expireDate = customer.data.membership.expireDate ? new Date(customer.data.membership.expireDate) : null
    const isValid = expireDate && expireDate > now

    // 计算剩余天数
    let remainingDays = 0
    if (isValid && expireDate) {
      const diffTime = expireDate.getTime() - now.getTime()
      remainingDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    }

    // 构建会员信息
    const membershipInfo = {
      isMember: isValid,
      cardId: customer.data.membership.cardId,
      expireDate: customer.data.membership.expireDate,
      remainingTrainingCount: customer.data.membership.remainingTrainingCount || 0,
      purchaseDate: customer.data.membership.purchaseDate,
      renewalHistory: customer.data.membership.renewalHistory || [],
      isValid,
      remainingDays,
      card: membershipCard
    }

    // 添加会员特权信息
    if (isValid && membershipCard && membershipCard.membershipInfo) {
      membershipInfo.level = membershipCard.membershipInfo.level
      membershipInfo.privileges = membershipCard.membershipInfo.privileges
      membershipInfo.benefits = membershipCard.membershipInfo.benefits
    }

    return {
      success: true,
      data: membershipInfo
    }
  } catch (error) {
    return {
      success: false,
      message: '获取客户会员信息失败',
      error
    }
  }
}
